import http from '@/http/api.js'

// 获取业务列表
const getWorkerOrder = (params) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/externalPage',
		method: 'get',
        params
	})
}
const signIn = (data) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/signIn',
		method: 'POST',
        data
	})
}
const startWorkerOrder = (id) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/acceptOrder',
		method: 'POST',
        params: {id}
	})
}

const finishWorkerOrder = (data) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/completeOrder',
		method: 'POST',
		data
	})
}
const getWorkerOrderDetail = (id) => {
	return http.request({
		url: '/vt-admin/mini/sealContractObject/detail',
		method: 'get',
        params: {id}
	})
}
//  申请结算
const applySettlement = (data) => {
	return http.request({
		url: '/vt-admin/sealContractObjectPayment/save',
		method: 'POST',
        data
	})
}
export default {
	getWorkerOrder,
    startWorkerOrder,
	finishWorkerOrder,
    getWorkerOrderDetail,
	applySettlement
}