@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
.workbench-wrap {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 32rpx 0 0 0;
}
.summary-card {
  margin: 32rpx 32rpx 24rpx 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.06);
  padding: 32rpx 24rpx 24rpx 24rpx;
}
.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #333;
}
.summary-stats {
  display: flex;
  justify-content: space-between;
}
.stat-item {
  flex: 1;
  text-align: center;
}
.stat-label {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 8rpx;
}
.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d8cf0;
}
.stat-item.total .stat-value {
  color: #2d8cf0;
}
.stat-item.pending .stat-value {
  color: #ff9900;
}
.stat-item.processing .stat-value {
  color: #19be6b;
}
.stat-item.finished .stat-value {
  color: #909399;
}
.order-tab-bar {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin: 0 32rpx 16rpx 32rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);
  overflow: hidden;
}
.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #888;
  background: #fff;
  transition: all 0.2s;
}
.tab-item.active {
  color: #2d8cf0;
  font-weight: bold;
  background: #e6f7ff;
}
.order-list {
  margin: 0 32rpx;
}
.order-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.04);
  margin-bottom: 24rpx;
  padding: 24rpx 20rpx 16rpx 20rpx;
  transition: box-shadow 0.2s;
}
.order-actions {
  display: flex;
  gap: 24rpx;
  border-top: 1px solid #f0f0f0;
  margin-top: 18rpx;
  padding-top: 18rpx;
  justify-content: flex-end;
  background: #fff;
}
.action-btn {
  min-width: 120rpx;
  padding: 0 32rpx;
  height: 56rpx;
  line-height: 56rpx;
  border: none;
  border-radius: 32rpx;
  background: #f5f7fa;
  color: #2d8cf0;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0;
  outline: none;
  box-shadow: 0 2rpx 8rpx 0 rgba(45, 140, 240, 0.04);
  transition: background 0.2s, color 0.2s;
}
.action-btn.primary {
  background: linear-gradient(90deg, #2d8cf0 0%, #57a3f3 100%);
  color: #fff;
}
.action-btn:active {
  opacity: 0.85;
}
.order-card:hover {
  box-shadow: 0 8rpx 32rpx 0 rgba(45, 140, 240, 0.08);
}
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.order-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.order-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
  background: #f5f7fa;
}
.order-status.status-pending {
  color: #ff9900;
  background: #fff7e6;
}
.order-status.status-processing {
  color: #19be6b;
  background: #e6ffed;
}
.order-status.status-finished {
  color: #909399;
  background: #f4f4f5;
}
.order-detail-row {
  display: flex;
  align-items: flex-start;
  font-size: 26rpx;
  color: #555;
  margin-bottom: 8rpx;
}
.order-label {
  min-width: 120rpx;
  color: #888;
  font-weight: 400;
}
.order-value {
  flex: 1;
  color: #333;
  word-break: break-all;
}
.empty-tip {
  text-align: center;
  color: #bbb;
  font-size: 28rpx;
  margin: 64rpx 0;
}

