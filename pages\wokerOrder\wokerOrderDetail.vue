<template>
	<view>
		<view class="wrap">
			<view class="content_box" style="width: 100vw; box-sizing: border-box" :style="{ padding: '20rpx' }">
				<uni-card :border="false" margin="0 0 20rpx 0" padding="0" :is-shadow="false">
					<uni-section title="基本信息" type="line">
						<template #right>
							<uni-tag inverted :type="
                  detailForm.objectStatus == 0 || detailForm.objectStatus == 1
                    ? 'warning'
                    : 'success'
                " :text="['待接单', '进行中', '已完成'][detailForm.objectStatus]"></uni-tag>
						</template>
						<uni-list>
							<uni-list-item title="工单名称" :rightText="detailForm.objectName" />
							<uni-list-item title="工单类型" :rightText="detailForm.labelName" />
							<uni-list-item title="派单人" :rightText="detailForm.createName" />
							<uni-list-item title="服务时间" :rightText="detailForm.planTime" />
							<uni-list-item title="工单价格" :rightText="detailForm.orderPrice" />
							<uni-list-item title="工单描述" :note="detailForm.remark" />
						</uni-list>
					</uni-section>
				</uni-card>
				<uni-card :border="false" margin="0 0 20rpx 0" padding="0" :is-shadow="false">
					<uni-section title="联系信息" type="line">
						<uni-list>
							<uni-list-item title="联系人" :rightText="detailForm.objectName" />

							<uni-list-item title="联系电话" :rightText="detailForm.contact" />

							<uni-list-item title="服务地址" :note="detailForm.distributionAddress" />
						</uni-list>
					</uni-section>
				</uni-card>
				<uni-card :border="false" margin="0 0 20rpx 0" padding="0" :is-shadow="false">
					<uni-section title="完成信息" type="line">
						<uni-list>
							<uni-list-item title="完成时间" :rightText="detailForm.completeTime" />
							<uni-list-item title="备注" :note="detailForm.completeRemark" />
							<uni-list-item direction="column" title="工单附件">
								<template v-slot:footer>
									<uv-upload accept="media" disabled :maxCount="detailForm.completeFileList.length"
										:deletable="false" :fileList="detailForm.completeFileList" multiple>
									</uv-upload>
								</template>
							</uni-list-item>
						</uni-list>
					</uni-section>
				</uni-card>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				detailForm: {},
			};
		},
		components: {},
		onLoad({
			id
		}) {
			if (id) {
				this.getWorkerOrderDetail(id);
			}
		},

		methods: {
			getWorkerOrderDetail(id) {
				this.$u.api.getWorkerOrderDetail(id).then((res) => {
					this.detailForm = res.data;
					this.detailForm.completeFileList = this.detailForm.completeFileList.map(
						(item) => {
							return {
								...item,
								url: item.link,
							};
						}
					);
					console.log(this.detailForm.completeFileList);

				});
			},
		},
	};
</script>

<style lang="scss" scoped>
	.wrap {
		background: linear-gradient(to bottom, $u-primary, #fff);
		height: 40vh;
	}
</style>