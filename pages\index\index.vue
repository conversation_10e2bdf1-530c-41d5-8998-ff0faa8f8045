<template>
  <view class="workbench-wrap">
    <!-- 工单概况统计卡片 -->
    <view class="summary-card">
      <view class="summary-title">工单概况</view>
      <view class="summary-stats">
        <view class="stat-item total">
          <view class="stat-label">工单总数</view>
          <view class="stat-value">{{ summary.total }}</view>
        </view>
        <view class="stat-item pending">
          <view class="stat-label">待接单</view>
          <view class="stat-value">{{ summary.pending }}</view>
        </view>
        <view class="stat-item processing">
          <view class="stat-label">进行中</view>
          <view class="stat-value">{{ summary.processing }}</view>
        </view>
        <view class="stat-item finished">
          <view class="stat-label">已完成</view>
          <view class="stat-value">{{ summary.finished }}</view>
        </view>
      </view>
    </view>

    <!-- 工单tab栏 -->
    <view class="order-tab-bar">
      <view
        v-for="(tab, idx) in tabs"
        :key="tab.value"
        :class="['tab-item', { active: currentTab === idx }]"
        @tap="handleClickTab(idx)"
      >
        {{ tab.label }}
      </view>
    </view>

    <!-- 工单列表 -->
    <view class="order-list">
      <view v-if="orders.length === 0" class="empty-tip">
        暂无工单
      </view>
      <view v-for="order in orders" :key="order.id" class="order-card">
        <view class="order-header">
          <view class="order-title">{{ order.objectName }}</view>
          <view class="order-status" :class="'status-' + order.objectStatus">{{
            statusMap[order.objectStatus]
          }}</view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">服务类型：</view>
          <view class="order-value">{{ order.serverTypeName }}</view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">服务时间：</view>
          <view class="order-value">{{ order.serviceStartTime }} - {{ order.serviceEndTime }}</view>
        </view>
        <!-- <view class="order-detail-row">
          <view class="order-label">工单描述：</view>
          <view class="order-value">{{ order.taskDescription }}</view>
        </view> -->
        <view class="order-detail-row">
          <view class="order-label">客户名称：</view>
          <view class="order-value">{{ order.customerName }}</view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">联系人：</view>
          <view class="order-value">{{ order.contact }}</view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">联系电话：</view>
          <view class="order-value">{{ order.contactPhone }}</view>
        </view>
        <view class="order-detail-row">
          <view class="order-label">地址：</view>
          <view class="order-value">{{ order.distributionAddress }}</view>
        </view>
        <!-- 操作区 -->
        <view class="order-actions">
          <template v-if="order.objectStatus == 3">
            <u-button   type="primary"
              plain class="action-btn primary" @tap="handleAccept(order)"
              >接单</u-button
            >
          </template>
          <template   v-if="order.objectStatus == 1 && order.isNeedSign == 1 && order.isSign == 0">
            <u-button
              class="action-btn"
              type="primary"
              plain
              hairline
              @tap="handleSign(order)"
              >签到</u-button
            >
            <u-button class="action-btn primary"  v-if="order.isNeedSign == 1 ? order.isSign == 1 : order.objectStatus == 1" @tap="handleFinish(order)">完成</u-button>
          </template>
        </view>
      </view>
    </view>
    <!-- 签到抽屉 -->
    <u-popup
      :show="showSignDrawer"
      type="bottom"
      closeOnClickOverlay
      @close="showSignDrawer = false"
      :mask-click="true"
      background="#fff"
      style="z-index: 9999"
    >
      <view style="padding: 32rpx 24rpx">
        <view style="font-size: 32rpx; font-weight: bold; margin-bottom: 24rpx"
          >签到</view
        >
        <view style="margin-bottom: 24rpx">
          <view style="font-size: 28rpx; margin-bottom: 8rpx">选择位置</view>
          <u-button
            @tap="chooseLocation"
            type="primary"
            :loading="locationLoading"
            loadingText="正在获取位置..."
            plain
          >
            {{ signAddress ? signAddress : "点击获取位置" }}
          </u-button>
        </view>
        <view style="margin-bottom: 24rpx">
          <view style="font-size: 28rpx; margin-bottom: 8rpx">上传照片</view>
          <uv-upload
            accept="media"
            @clickPreview="handleClickPreview"
            :fileList="signPhoto"
            @afterRead="afterRead"
            @delete="handleDelete"
            multiple
            :maxCount="9"
          >
          </uv-upload>
        </view>
        <!-- <view style="margin-bottom: 24rpx">
          <view style="font-size: 28rpx; margin-bottom: 8rpx">备注</view>
          <u-input
            v-model="signRemark"
            placeholder="请输入备注"
            type="textarea"
            border
          />
        </view> -->
        <u-button type="primary" @tap="submitSign">确认签到</u-button>
      </view>
    </u-popup>

    <!-- 接单 -->
     <u-modal
      :show="acceptOrderModalShow"
      @confirm="acceptOrderConfirm"
      ref="uModal"
      title="确认接单"
      content="确认接单吗？"
      showCancelButton
      @cancel="acceptOrderModalShow = false"
      :asyncClose="true"
    ></u-modal>
  </view>
</template>

<script>
import wokerOrderApi from "@/api/wokerOrder.js";
import QQMapWX from "@/utils/qqmap-wx-jssdk.min.js";
import http from '../../http/api.js'
export default {
  data() {
    return {
      summary: {
        total: 0,
        pending: 0,
        processing: 0,
        finished: 0,
      },
      tabs: [
        { label: "进行中", value: "processing" },
        { label: "待接单", value: "pending" },
      ],
      currentTab: 0,
      orders: [],
      statusMap: {
        3: "待接单",
        1: "进行中",
        2: "已完成",
      },
      showSignDrawer: false,
      locationLoading: false,
      signAddress: null,
      signPhoto: "",
      signRemark: "",
      signOrder: null,
      // 初始化地图实例
      qqmapsdk: null,

      // 接单
      currentItem:null,
      acceptOrderModalShow: false,

    };
  },

  computed: {
    filteredOrders() {
      const status = this.tabs[this.currentTab].value;
      return this.orders.filter((o) => o.status === status);
    },
  },
  async onLoad() {
    await this.wxlogin();
    this.fetchOrders();
    // 实例化，填入你申请到的 Key
    this.qqmapsdk = new QQMapWX({
      key: "V37BZ-5JF63-HBZ3S-34XAJ-KPG2Q-74FPM", // 请替换为你的真实 Key
    });
  },
  methods: {
    wxlogin() {
      return new Promise((resolve) => {
        uni.login({
          complete: (res) => {
            this.$u.api
              .wxToken({ code: res.code })
              .then((data) => {
                this.$u.func.login(data);
                resolve();
              })
              .catch((err) => {
                this.$u.func.showToast({ title: err });
              });
          },
        });
      });
    },
    handleClickTab(idx) {
      this.currentTab = idx;
      this.fetchOrders();
    },
    async fetchOrders() {
      const map = {
        0:1,
        1:3
      }
      this.$u.api.getWorkerOrder({
        pageNum: 1,
        pageSize: 10,
        objectStatus: map[this.currentTab],
        
      }).then((data) => {
        this.orders = data.data.records || [];
        // this.summary = data.summary || {};
      });
    },
    mapStatus(status) {
      // 假设后端status: 0=待接单, 1=进行中, 2=已完成
      if (status === 0) return "pending";
      if (status === 1) return "processing";
      if (status === 2) return "finished";
      return "pending";
    },
    //接单
    handleAccept(item) {
      this.currentItem = item;
      this.acceptOrderModalShow = true;
    },
    acceptOrderConfirm() {
      this.$u.api.startWorkerOrder(this.currentItem.id).then((res) => {
        this.acceptOrderModalShow = false;
        this.fetchOrders();
      });
    },
    handleSign(order) {
      this.signOrder = order;
      this.showSignDrawer = true;
      this.signAddress = null;
      this.signPhoto = [];
      this.signRemark = "";
      this.chooseLocation();
    },
    chooseLocation() {
      // wx.chooseLocation({
      //   success: (res) => {
      //     this.signAddress = res;
      //   },
      //   fail: (err) => {
      // 	console.log(err);

      //     uni.showToast({ title: '位置选择失败', icon: 'none' });
      //   }
      // });
      this.locationLoading = true;
      wx.getLocation({
        type: "gcj02",

        success: (res) => {
          console.log(res);

          // 成功获取经纬度后，进行逆地址解析
          this.qqmapsdk.reverseGeocoder({
            location: {
              latitude: res.latitude,
              longitude: res.longitude,
            },
            success: (result) => {
              // 逆解析成功回调
              console.log("逆地址解析结果：", result);
              // 详细的地址信息在 result.result 里
              const addressInfo = result.result.address_component;
              const formattedAddress = result.result.address;
              console.log("所在城市：", addressInfo.city);
              console.log("完整地址：", formattedAddress);
              this.locationLoading = false;
              this.signAddress = formattedAddress;
              // 你可以在这里将地址信息更新到 data 中，或进行其他操作
            },
            fail: function (err) {
              this.locationLoading = false;
              console.error("逆地址解析失败：", err);
              uni.showToast({ title: "位置解析失败", icon: "none" });
            },
          });
        },
        fail: (err) => {
          console.log(err);
          this.locationLoading = false;
          uni.showToast({ title: "位置选择失败", icon: "none" });
        },
      });
    },
    afterRead(event) {
      console.log(event);
      const { file } = event;
      const indexAll = this.signPhoto.length;
      file.forEach((item, index) => {
        this.signPhoto.push({
          ...item,
          status: "uploading",
          message: "上传中",
          // url: item.thumb,
          index: indexAll + index,
        });
      });
      file.forEach((item, index) => {
        this.uploadFile(item.url, indexAll + index);
      });
    },
    uploadFile(url, index) {
      return new Promise((resolve) => {
        const params = {
          filePath: url,
          name: "file",
        };
        http.upload("/blade-resource/attach/upload", params).then((res) => {
          this.signPhoto.find((item) => item.index == index).status =
            "success";
          this.signPhoto.find((item) => item.index == index).message =
            "";
          this.signPhoto.find((item) => item.index == index).url =
            res.data.link;
        });
      });
    },
    handleDelete({ file, index, name }) {
      console.log(file, index, name);
      this.signPhoto.splice(index, 1);
    },
    handleClickPreview(url, lists, name) {
      console.log(url, lists, name);
    },
    submitSign() {
      // if (!this.signAddress) {
      //   uni.showToast({ title: "请获取位置", icon: "none" });
      //   return;
      // }
      if (!this.signPhoto) {
        uni.showToast({ title: "请上传照片", icon: "none" });
        return;
      }
      // 这里可以提交签到数据
      const data = {
        objectId: this.signOrder.id,
        address: this.signAddress,
        photo: this.signPhoto.map((item) => item.url).join(","),
        // remark: this.signRemark,
      };
      this.$u.api.signIn(data).then((res) => {
        console.log(res);
      }).then(() => {
        uni.showToast({ title: "签到成功", icon: "success" });
        this.showSignDrawer = false;
        this.fetchOrders();
      });
    },
    handleFinish(order) {
      uni.showToast({ title: `完成：${order.name}`, icon: "none" });
    },
  },
};
</script>

<style lang="scss">
.workbench-wrap {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 32rpx 0 0 0;
}
.summary-card {
  margin: 32rpx 32rpx 24rpx 32rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.06);
  padding: 32rpx 24rpx 24rpx 24rpx;
}
.summary-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #333;
}
.summary-stats {
  display: flex;
  justify-content: space-between;
}
.stat-item {
  flex: 1;
  text-align: center;
}
.stat-label {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 8rpx;
}
.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d8cf0;
}
.stat-item.total .stat-value {
  color: #2d8cf0;
}
.stat-item.pending .stat-value {
  color: #ff9900;
}
.stat-item.processing .stat-value {
  color: #19be6b;
}
.stat-item.finished .stat-value {
  color: #909399;
}

.order-tab-bar {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin: 0 32rpx 16rpx 32rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);
  overflow: hidden;
}
.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #888;
  background: #fff;
  transition: all 0.2s;
}
.tab-item.active {
  color: #2d8cf0;
  font-weight: bold;
  background: #e6f7ff;
}

.order-list {
  margin: 0 32rpx;
}
.order-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.04);
  margin-bottom: 24rpx;
  padding: 24rpx 20rpx 16rpx 20rpx;
  transition: box-shadow 0.2s;
}
.order-actions {
  display: flex;
  gap: 24rpx;
  border-top: 1px solid #f0f0f0;
  margin-top: 18rpx;
  padding-top: 18rpx;
  justify-content: flex-end;
  background: #fff;
}
.action-btn {
  min-width: 120rpx;
  padding: 0 32rpx;
  height: 56rpx;

  line-height: 56rpx;
  border: none;
  border-radius: 32rpx;
  background: #f5f7fa;
  color: #2d8cf0;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0;
  outline: none;
  box-shadow: 0 2rpx 8rpx 0 rgba(45, 140, 240, 0.04);
  transition: background 0.2s, color 0.2s;
}
.action-btn.primary {
  background: linear-gradient(90deg, #2d8cf0 0%, #57a3f3 100%);
  color: #fff;
}
.action-btn:active {
  opacity: 0.85;
}
.order-card:hover {
  box-shadow: 0 8rpx 32rpx 0 rgba(45, 140, 240, 0.08);
}
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.order-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.order-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
  background: #f5f7fa;
}
.order-status.status-pending {
  color: #ff9900;
  background: #fff7e6;
}
.order-status.status-processing {
  color: #19be6b;
  background: #e6ffed;
}
.order-status.status-finished {
  color: #909399;
  background: #f4f4f5;
}
.order-detail-row {
  display: flex;
  align-items: flex-start;
  font-size: 26rpx;
  color: #555;
  margin-bottom: 8rpx;
}
.order-label {
  min-width: 120rpx;
  color: #888;
  font-weight: 400;
}
.order-value {
  flex: 1;
  color: #333;
  word-break: break-all;
}
.empty-tip {
  text-align: center;
  color: #bbb;
  font-size: 28rpx;
  margin: 64rpx 0;
}
</style>
