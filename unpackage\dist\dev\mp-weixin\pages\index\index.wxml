<view class="workbench-wrap"><view class="summary-card"><view class="summary-title">工单概况</view><view class="summary-stats"><view class="stat-item total"><view class="stat-label">工单总数</view><view class="stat-value">{{summary.total}}</view></view><view class="stat-item pending"><view class="stat-label">待接单</view><view class="stat-value">{{summary.pending}}</view></view><view class="stat-item processing"><view class="stat-label">进行中</view><view class="stat-value">{{summary.processing}}</view></view><view class="stat-item finished"><view class="stat-label">已完成</view><view class="stat-value">{{summary.finished}}</view></view></view></view><view class="order-tab-bar"><block wx:for="{{tabs}}" wx:for-item="tab" wx:for-index="idx" wx:key="value"><view data-event-opts="{{[['tap',[['handleClickTab',[idx]]]]]}}" class="{{['tab-item',[(currentTab===idx)?'active':'']]}}" bindtap="__e">{{''+tab.label+''}}</view></block></view><view class="order-list"><block wx:if="{{$root.g0===0}}"><view class="empty-tip">暂无工单</view></block><block wx:for="{{orders}}" wx:for-item="order" wx:for-index="__i0__" wx:key="id"><view class="order-card"><view class="order-header"><view class="order-title">{{order.objectName}}</view><view class="{{['order-status','status-'+order.objectStatus]}}">{{statusMap[order.objectStatus]}}</view></view><view class="order-detail-row"><view class="order-label">服务类型：</view><view class="order-value">{{order.serverTypeName}}</view></view><view class="order-detail-row"><view class="order-label">服务时间：</view><view class="order-value">{{order.serviceStartTime+" - "+order.serviceEndTime}}</view></view><view class="order-detail-row"><view class="order-label">客户名称：</view><view class="order-value">{{order.customerName}}</view></view><view class="order-detail-row"><view class="order-label">联系人：</view><view class="order-value">{{order.contact}}</view></view><view class="order-detail-row"><view class="order-label">联系电话：</view><view class="order-value">{{order.contactPhone}}</view></view><view class="order-detail-row"><view class="order-label">地址：</view><view class="order-value">{{order.distributionAddress}}</view></view><view class="order-actions"><block wx:if="{{order.objectStatus==3}}"><u-button class="action-btn primary" vue-id="{{'8dd740cc-1-'+__i0__}}" type="primary" plain="{{true}}" data-event-opts="{{[['^tap',[['handleAccept',['$0'],[[['orders','id',order.id]]]]]]]}}" bind:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">接单</u-button></block><block wx:if="{{order.objectStatus==1&&order.isNeedSign==1&&order.isSign==0}}"><u-button class="action-btn" vue-id="{{'8dd740cc-2-'+__i0__}}" type="primary" plain="{{true}}" hairline="{{true}}" data-event-opts="{{[['^tap',[['handleSign',['$0'],[[['orders','id',order.id]]]]]]]}}" bind:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">签到</u-button><block wx:if="{{order.isNeedSign==1?order.isSign==1:order.objectStatus==1}}"><u-button class="action-btn primary" bind:tap="__e" vue-id="{{'8dd740cc-3-'+__i0__}}" data-event-opts="{{[['^tap',[['handleFinish',['$0'],[[['orders','id',order.id]]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}">完成</u-button></block></block></view></view></block></view><u-popup style="z-index:9999;" vue-id="8dd740cc-4" show="{{showSignDrawer}}" type="bottom" closeOnClickOverlay="{{true}}" mask-click="{{true}}" background="#fff" data-event-opts="{{[['^close',[['e0']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><view style="padding:32rpx 24rpx;"><view style="font-size:32rpx;font-weight:bold;margin-bottom:24rpx;">签到</view><view style="margin-bottom:24rpx;"><view style="font-size:28rpx;margin-bottom:8rpx;">选择位置</view><u-button vue-id="{{('8dd740cc-5')+','+('8dd740cc-4')}}" type="primary" loading="{{locationLoading}}" loadingText="正在获取位置..." plain="{{true}}" data-event-opts="{{[['^tap',[['chooseLocation']]]]}}" bind:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">{{''+(signAddress?signAddress:"点击获取位置")+''}}</u-button></view><view style="margin-bottom:24rpx;"><view style="font-size:28rpx;margin-bottom:8rpx;">上传照片</view><uv-upload vue-id="{{('8dd740cc-6')+','+('8dd740cc-4')}}" accept="media" fileList="{{signPhoto}}" multiple="{{true}}" maxCount="{{9}}" data-event-opts="{{[['^clickPreview',[['handleClickPreview']]],['^afterRead',[['afterRead']]],['^delete',[['handleDelete']]]]}}" bind:clickPreview="__e" bind:afterRead="__e" bind:delete="__e" bind:__l="__l"></uv-upload></view><u-button vue-id="{{('8dd740cc-7')+','+('8dd740cc-4')}}" type="primary" data-event-opts="{{[['^tap',[['submitSign']]]]}}" bind:tap="__e" bind:__l="__l" vue-slots="{{['default']}}">确认签到</u-button></view></u-popup><u-modal class="vue-ref" vue-id="8dd740cc-8" show="{{acceptOrderModalShow}}" title="确认接单" content="确认接单吗？" showCancelButton="{{true}}" asyncClose="{{true}}" data-ref="uModal" data-event-opts="{{[['^confirm',[['acceptOrderConfirm']]],['^cancel',[['e1']]]]}}" bind:confirm="__e" bind:cancel="__e" bind:__l="__l"></u-modal></view>