{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/project/vt-unih5-order/pages/person/userInfo.vue?8f10", "webpack:///D:/project/vt-unih5-order/pages/person/userInfo.vue?6585", "webpack:///D:/project/vt-unih5-order/pages/person/userInfo.vue?48d4", "uni-app:///pages/person/userInfo.vue", "webpack:///D:/project/vt-unih5-order/pages/person/userInfo.vue?15f8", "webpack:///D:/project/vt-unih5-order/pages/person/userInfo.vue?170b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "avatar", "name", "phone", "onLoad", "methods", "getDetail", "onChooseAvatar", "getphonenumber", "console", "code", "v", "handleSave", "filePath", "http", "uni", "title", "success", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qUAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC2CznB;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACAC;MACA,IACAC,OACAC,SADAD;MAEA;QACA;UACA;QACA;MACA;IAEA;IAEAE;MAAA;MACAH;MACA,6BACA,cACA;MACA;QACA;QACA;UACAI;UACAX;QACA;QACAY;UACAL;UACAV,uCACA;YACAE;UAAA,EACA;UACA;YACAc;cACAC;cACAC;gBACAC;kBACAH;gBACA;cACA;YACA;UAEA;QACA;MACA;QACA;UACAA;YACAC;YACAC;cACAC;gBACAH;cACA;YACA;UACA;QAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3HA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/person/userInfo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/person/userInfo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./userInfo.vue?vue&type=template&id=327543aa&scoped=true&\"\nvar renderjs\nimport script from \"./userInfo.vue?vue&type=script&lang=js&\"\nexport * from \"./userInfo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./userInfo.vue?vue&type=style&index=0&id=327543aa&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"327543aa\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/person/userInfo.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userInfo.vue?vue&type=template&id=327543aa&scoped=true&\"", "var components\ntry {\n  components = {\n    uCellGroup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell-group/u-cell-group\" */ \"@/uni_modules/uview-ui/components/u-cell-group/u-cell-group.vue\"\n      )\n    },\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell/u-cell\" */ \"@/uni_modules/uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userInfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userInfo.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"padding: 20rpx;box-sizing: border-box;\">\r\n\t\t<u-cell-group>\r\n\t\t\t<u-cell title=\"头像\">\r\n\t\t\t\t<template #value>\r\n\t\t\t\t\t<button\r\n\t\t\t\t\t\tstyle=\"display: flex;align-items: center;justify-content: center;width: 70rpx; height: 70rpx;padding: 5rpx;\"\r\n\t\t\t\t\t\topen-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\">\r\n\t\t\t\t\t\t<image :src=\"userInfo.avatar\" style=\"width: 70rpx; height: 70rpx;\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</button>\r\n\r\n\t\t\t\t</template>\r\n\t\t\t</u-cell>\r\n\t\t\t<u-cell title=\"昵称\" value=\"\">\r\n\t\t\t\t<template #value>\r\n\t\t\t\t\t<u-input v-model=\"userInfo.name\" placeholder=\"请输入昵称\" type=\"nickname\" border></u-input>\r\n\t\t\t\t</template>\r\n\t\t\t</u-cell>\r\n\t\t\t<u-cell title=\"真实姓名\" value=\"\">\r\n\t\t\t\t<template #value>\r\n\t\t\t\t\t<u-input v-model=\"userInfo.realName\" placeholder=\"请输入姓名\" type=\"nickname\" border></u-input>\r\n\t\t\t\t</template>\r\n\t\t\t</u-cell>\r\n\t\t\t<u-cell title=\"手机号\" value=\"\">\r\n\t\t\t\t<template #value>\r\n\t\t\t\t\t<u-input style=\"width: 300rpx;\" v-model=\"userInfo.phone\" readonly placeholder=\"请输入手机号\" type='number' border>\r\n\t\t\t\t\t\t<template slot=\"suffix\">\r\n\t\t\t\t\t\t\t<!-- <u-button :text=\"'快捷获取'\" @getphonenumber=\"getphonenumber\" type=\"primary\"\r\n\t\t\t\t\t\t\t\topen-type=\"getPhoneNumber\" size=\"mini\"></u-button> -->\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t</u-input>\r\n\t\t\t\t</template>\r\n\t\t\t</u-cell>\r\n\t\t</u-cell-group>\r\n\t\t<view class=\"\" style=\"margin-top: 20rpx;\">\r\n\t\t\t<u-button size=\"large\" type=\"primary\" @click=\"handleSave\" text=\"保存\"></u-button>\r\n\t\t</view>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport http from '../../http/api.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserInfo: {\r\n\t\t\t\t\tavatar: '',\r\n\t\t\t\t\tname: '',\r\n\t\t\t\t\tphone: ''\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.getDetail()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetDetail() {\r\n\t\t\t\tthis.$u.api.userInfo().then((res) => {\r\n\t\t\t\t\tthis.userInfo = res.data\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonChooseAvatar(v) {\r\n\t\t\t\tthis.userInfo.avatar = v.detail.avatarUrl\r\n\t\t\t},\r\n\t\t\tgetphonenumber(v) {\r\n\t\t\t\tconsole.log(v);\r\n\t\t\t\tconst {\r\n\t\t\t\t\tcode\r\n\t\t\t\t} = v.detail\r\n\t\t\t\tif (code) {\r\n\t\t\t\t\tthis.$u.api.userPhone(code).then(res => {\r\n\t\t\t\t\t\tthis.userInfo.phone = res.data.phoneNumber\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\r\n\t\t\thandleSave() {\r\n\t\t\t\tconsole.log(1111)\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\t...this.userInfo\r\n\t\t\t\t}\r\n\t\t\t\tif (this.userInfo.avatar && this.userInfo.avatar.indexOf('http') < 0) {\r\n\t\t\t\t\t// 上传头像\r\n\t\t\t\t\tconst params = {\r\n\t\t\t\t\t\tfilePath: this.userInfo.avatar,\r\n\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t}\r\n\t\t\t\t\thttp.upload('/blade-resource/attach/upload?fileName=', params).then(res => {\r\n\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\tdata = {\r\n\t\t\t\t\t\t\t...this.userInfo,\r\n\t\t\t\t\t\t\tavatar: res.data.link\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.$u.api.updateUser(data).then(res => {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '操作成功',\r\n\t\t\t\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$u.api.updateUser(data).then(res => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '操作成功',\r\n\t\t\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t\t\t}, 1000)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.slot-image {\r\n\t\theight: 60rpx;\r\n\t\twidth: 60rpx;\r\n\t}\r\n\r\n\t::v-deep input {\r\n\t\ttext-align: right !important;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userInfo.vue?vue&type=style&index=0&id=327543aa&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userInfo.vue?vue&type=style&index=0&id=327543aa&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758953794593\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}