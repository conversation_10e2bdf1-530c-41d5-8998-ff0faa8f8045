{
	"name": "vt-unih5_customer",
	"appid": "__UNI__B06A6D1",
	"description": "",
	"versionName": "1.0.0",
	"versionCode": "100",
	"transformPx": false,
	"app-plus": {
		/* 5+App特有相关 */
		"usingComponents": true,
		"nvueCompiler": "uni-app",
		"nvueStyleCompiler": "uni-app",
		"splashscreen": {
			"alwaysShowBeforeRender": true,
			"waiting": true,
			"autoclose": true,
			"delay": 0
		},
		"modules": {},
		/* 模块配置 */
		"distribute": {
			/* 应用发布信息 */
			"android": {
				/* android打包配置 */
				"permissions": [
					"<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
					"<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
					"<uses-permission android:name=\"android.permission.VIBRATE\"/>",
					"<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
					"<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
					"<uses-permission android:name=\"android.permission.CAMERA\"/>",
					"<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
					"<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
					"<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
					"<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
					"<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
					"<uses-feature android:name=\"android.hardware.camera\"/>",
					"<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
				]
			},
			"ios": {},
			/* ios打包配置 */
			"sdkConfigs": {}
		}
	},
	/* SDK配置 */
	"quickapp": {},
	/* 快应用特有相关 */
	"mp-weixin": {
		"appid": "wxc04e5225a797878b",
		"setting": {
			"urlCheck": false
		},
		"usingComponents": true,
		"permission": {
			"scope.userLocation": {
				"desc": "工单上门服务 需要获取位置签到"
			}
		}
	},
	"vueVersion": "2"
}