@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
.uni-badge--x {
  display: inline-block;
  position: relative;
}
.uni-badge--absolute {
  position: absolute;
}
.uni-badge--small {
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}
.uni-badge {
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
  -webkit-font-feature-settings: "tnum";
          font-feature-settings: "tnum";
  min-width: 20px;
  justify-content: center;
  flex-direction: row;
  height: 20px;
  padding: 0 4px;
  line-height: 18px;
  color: #fff;
  border-radius: 100px;
  background-color: #8f939c;
  background-color: transparent;
  border: 1px solid #fff;
  text-align: center;
  font-family: 'Helvetica Neue', Helvetica, sans-serif;
  font-size: 12px;
}
.uni-badge--info {
  color: #fff;
  background-color: #8f939c;
}
.uni-badge--primary {
  background-color: #2979ff;
}
.uni-badge--success {
  background-color: #18bc37;
}
.uni-badge--warning {
  background-color: #f3a73f;
}
.uni-badge--error {
  background-color: #e43d33;
}
.uni-badge--inverted {
  padding: 0 5px 0 0;
  color: #8f939c;
}
.uni-badge--info-inverted {
  color: #8f939c;
  background-color: transparent;
}
.uni-badge--primary-inverted {
  color: #2979ff;
  background-color: transparent;
}
.uni-badge--success-inverted {
  color: #18bc37;
  background-color: transparent;
}
.uni-badge--warning-inverted {
  color: #f3a73f;
  background-color: transparent;
}
.uni-badge--error-inverted {
  color: #e43d33;
  background-color: transparent;
}

